{"version": 3, "file": "ExpoLocation.web.js", "sourceRoot": "", "sources": ["../src/ExpoLocation.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAE9F,OAAO,EACL,gBAAgB,GAIjB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,MAAM,aAAc,SAAQ,KAAK;IAC/B,IAAI,CAAS;IAEb;QACE,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,SAAS,yBAAyB,CAAC,QAAwB;IACzD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;IACvC,OAAO;QACL,MAAM,EAAE;YACN,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,KAAK,EAAE,MAAM,CAAC,KAAK;SACpB;QACD,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,QAAwB,EAAE,OAAiC;IAClF,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC9E,MAAM,gBAAgB,GACpB,OAAO,OAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;IACrF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;IAE9D,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,IAAI,gBAAgB,IAAI,gBAAgB,CAAC;AAC3F,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAAC,SAAS,GAAG,KAAK;IAClD,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;QACnC,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,4CAA4C,CAAC,CAAC;IAC/F,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAE9E,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;QACnC,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC;SACX,CAAC;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC;SACX,CAAC;IACJ,CAAC;IAED,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,SAAS,CAAC,WAAW,CAAC,kBAAkB,CACtC,GAAG,EAAE;gBACH,OAAO,CAAC;oBACN,MAAM,EAAE,gBAAgB,CAAC,OAAO;oBAChC,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,CAAC;iBACX,CAAC,CAAC;YACL,CAAC,EACD,CAAC,aAAuC,EAAE,EAAE;gBAC1C,IAAI,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,iBAAiB,EAAE,CAAC;oBAC3D,OAAO,CAAC;wBACN,MAAM,EAAE,gBAAgB,CAAC,MAAM;wBAC/B,OAAO,EAAE,KAAK;wBACd,WAAW,EAAE,IAAI;wBACjB,OAAO,EAAE,CAAC;qBACX,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC;oBACN,MAAM,EAAE,gBAAgB,CAAC,OAAO;oBAChC,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,CAAC;iBACX,CAAC,CAAC;YACL,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8EAA8E;IAC9E,yBAAyB;IACzB,OAAO;QACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;QACrC,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,CAAC;KACX,CAAC;AACJ,CAAC;AAED,IAAI,iBAAiB,GAA0B,IAAI,CAAC;AAEpD,eAAe;IACb,KAAK,CAAC,sBAAsB;QAC1B,OAAO;YACL,uBAAuB,EAAE,aAAa,IAAI,SAAS;SACpD,CAAC;IACJ,CAAC;IACD,KAAK,CAAC,yBAAyB,CAC7B,UAAoC,EAAE;QAEtC,IAAI,iBAAiB,IAAI,eAAe,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,CAAC;YACrE,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,CAAC,uBAAuB,CAAC,OAAwB;QACpD,OAAO,IAAI,OAAO,CAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAqB,CAAC,QAAQ,EAAE,EAAE;gBAC9C,iBAAiB,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBACxD,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC7B,CAAC,CAAC;YACF,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE;gBACzD,UAAU,EAAE,QAAQ;gBACpB,kBAAkB,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC,QAAQ;gBACvE,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IACD,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACzC,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACvE,CAAC;IACD,KAAK,CAAC,uBAAuB;QAC3B,OAAO,aAAa,IAAI,SAAS,CAAC;IACpC,CAAC;IACD,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,aAAa,EAAE,CAAC;IAC5B,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,aAAa,EAAE,CAAC;IAC5B,CAAC;IACD,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,OAAwB;QACpE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,aAAa,CAC3C,CAAC,QAAQ,EAAE,EAAE;gBACX,iBAAiB,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBACxD,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAChD,OAAO;oBACP,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC,CAAC;YACL,CAAC,EACD,SAAS,EACT,OAAO,CACR,CAAC;YACF,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iCAAiC;QACrC,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IACD,KAAK,CAAC,iCAAiC;QACrC,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;CACF,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus, UnavailabilityError } from 'expo-modules-core';\n\nimport {\n  LocationAccuracy,\n  LocationLastKnownOptions,\n  LocationObject,\n  LocationOptions,\n} from './Location.types';\nimport { LocationEventEmitter } from './LocationEventEmitter';\n\nclass GeocoderError extends Error {\n  code: string;\n\n  constructor() {\n    super('Geocoder service is not available for this device.');\n    this.code = 'E_NO_GEOCODER';\n  }\n}\n\n/**\n * Converts `GeolocationPosition` to JavaScript object.\n */\nfunction geolocationPositionToJSON(position: LocationObject): LocationObject {\n  const { coords, timestamp } = position;\n  return {\n    coords: {\n      latitude: coords.latitude,\n      longitude: coords.longitude,\n      altitude: coords.altitude,\n      accuracy: coords.accuracy,\n      altitudeAccuracy: coords.altitudeAccuracy,\n      heading: coords.heading,\n      speed: coords.speed,\n    },\n    timestamp,\n  };\n}\n\n/**\n * Checks whether given location didn't exceed given `maxAge` and fits in the required accuracy.\n */\nfunction isLocationValid(location: LocationObject, options: LocationLastKnownOptions): boolean {\n  const maxAge = typeof options.maxAge === 'number' ? options.maxAge : Infinity;\n  const requiredAccuracy =\n    typeof options.requiredAccuracy === 'number' ? options.requiredAccuracy : Infinity;\n  const locationAccuracy = location.coords.accuracy ?? Infinity;\n\n  return Date.now() - location.timestamp <= maxAge && locationAccuracy <= requiredAccuracy;\n}\n\n/**\n * Gets the permission details. The implementation is not very good as it's not\n * possible to query for permission on all browsers, apparently only the\n * latest versions will support this.\n */\nasync function getPermissionsAsync(shouldAsk = false): Promise<PermissionResponse> {\n  if (!navigator?.permissions?.query) {\n    throw new UnavailabilityError('expo-location', 'navigator.permissions API is not available');\n  }\n\n  const permission = await navigator.permissions.query({ name: 'geolocation' });\n\n  if (permission.state === 'granted') {\n    return {\n      status: PermissionStatus.GRANTED,\n      granted: true,\n      canAskAgain: true,\n      expires: 0,\n    };\n  }\n\n  if (permission.state === 'denied') {\n    return {\n      status: PermissionStatus.DENIED,\n      granted: false,\n      canAskAgain: true,\n      expires: 0,\n    };\n  }\n\n  if (shouldAsk) {\n    return new Promise((resolve) => {\n      navigator.geolocation.getCurrentPosition(\n        () => {\n          resolve({\n            status: PermissionStatus.GRANTED,\n            granted: true,\n            canAskAgain: true,\n            expires: 0,\n          });\n        },\n        (positionError: GeolocationPositionError) => {\n          if (positionError.code === positionError.PERMISSION_DENIED) {\n            resolve({\n              status: PermissionStatus.DENIED,\n              granted: false,\n              canAskAgain: true,\n              expires: 0,\n            });\n            return;\n          }\n\n          resolve({\n            status: PermissionStatus.GRANTED,\n            granted: false,\n            canAskAgain: true,\n            expires: 0,\n          });\n        }\n      );\n    });\n  }\n\n  // The permission state is 'prompt' when the permission has not been requested\n  // yet, tested on Chrome.\n  return {\n    status: PermissionStatus.UNDETERMINED,\n    granted: false,\n    canAskAgain: true,\n    expires: 0,\n  };\n}\n\nlet lastKnownPosition: LocationObject | null = null;\n\nexport default {\n  async getProviderStatusAsync(): Promise<{ locationServicesEnabled: boolean }> {\n    return {\n      locationServicesEnabled: 'geolocation' in navigator,\n    };\n  },\n  async getLastKnownPositionAsync(\n    options: LocationLastKnownOptions = {}\n  ): Promise<LocationObject | null> {\n    if (lastKnownPosition && isLocationValid(lastKnownPosition, options)) {\n      return lastKnownPosition;\n    }\n    return null;\n  },\n  async getCurrentPositionAsync(options: LocationOptions): Promise<LocationObject> {\n    return new Promise<LocationObject>((resolve, reject) => {\n      const resolver: PositionCallback = (position) => {\n        lastKnownPosition = geolocationPositionToJSON(position);\n        resolve(lastKnownPosition);\n      };\n      navigator.geolocation.getCurrentPosition(resolver, reject, {\n        maximumAge: Infinity,\n        enableHighAccuracy: (options.accuracy ?? 0) > LocationAccuracy.Balanced,\n        ...options,\n      });\n    });\n  },\n  async removeWatchAsync(watchId: number): Promise<void> {\n    navigator.geolocation.clearWatch(watchId);\n  },\n  async watchDeviceHeading(_headingId: number): Promise<void> {\n    console.warn('Location.watchDeviceHeading: is not supported on web');\n  },\n  async hasServicesEnabledAsync(): Promise<boolean> {\n    return 'geolocation' in navigator;\n  },\n  async geocodeAsync(): Promise<any[]> {\n    throw new GeocoderError();\n  },\n  async reverseGeocodeAsync(): Promise<any[]> {\n    throw new GeocoderError();\n  },\n  async watchPositionImplAsync(watchId: number, options: PositionOptions): Promise<number> {\n    return new Promise((resolve) => {\n      watchId = navigator.geolocation.watchPosition(\n        (position) => {\n          lastKnownPosition = geolocationPositionToJSON(position);\n          LocationEventEmitter.emit('Expo.locationChanged', {\n            watchId,\n            location: lastKnownPosition,\n          });\n        },\n        undefined,\n        options\n      );\n      resolve(watchId);\n    });\n  },\n\n  async requestForegroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync(true);\n  },\n  async requestBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync(true);\n  },\n  async getForegroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n  async getBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n};\n"]}