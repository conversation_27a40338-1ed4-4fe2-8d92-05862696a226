{"version": 3, "file": "memory.js", "sourceRoot": "", "sources": ["../src/memory.ts"], "names": [], "mappings": "AAAA,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAE9C,MAAM,CAAC,MAAM,YAAY,GAAsC,EAAE,CAAC;AAElE,8EAA8E;AAC9E,IAAI,KAAK,GAAgC,EAAE,CAAC;AAE5C,MAAM,UAAU,UAAU,CAAC,UAAkB;IAC3C,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,UAAkB;IAChD,OAAO,UAAU,IAAI,KAAK,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,UAAkB;IAC/C,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QACN,MAAM,iBAAiB,GAAa,cAAc,CAAC,cAAc,EAAE,CAAC;QAEpE,qEAAqE;QACrE,yEAAyE;QACzE,mEAAmE;QACnE,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,OAAO,UAAU,IAAI,KAAK,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,UAAkB;IACzD,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,UAAU;IACxB,KAAK,GAAG,EAAE,CAAC;AACb,CAAC", "sourcesContent": ["import ExpoFontLoader from './ExpoFontLoader';\n\nexport const loadPromises: { [name: string]: Promise<void> } = {};\n\n// cache the value on the js side for fast access to the fonts that are loaded\nlet cache: { [name: string]: boolean } = {};\n\nexport function markLoaded(fontFamily: string) {\n  cache[fontFamily] = true;\n}\n\nexport function isLoadedInCache(fontFamily: string): boolean {\n  return fontFamily in cache;\n}\n\nexport function isLoadedNative(fontFamily: string): boolean {\n  if (isLoadedInCache(fontFamily)) {\n    return true;\n  } else {\n    const loadedNativeFonts: string[] = ExpoFontLoader.getLoadedFonts();\n\n    // NOTE(brentvatne): Bail out here if there are no loaded fonts. This\n    // is functionally equivalent to the behavior below if the returned array\n    // is empty, but this handles improper mocking of `getLoadedFonts`.\n    if (!loadedNativeFonts?.length) {\n      return false;\n    }\n\n    loadedNativeFonts.forEach((font) => {\n      cache[font] = true;\n    });\n    return fontFamily in cache;\n  }\n}\n\nexport function purgeFontFamilyFromCache(fontFamily: string): void {\n  delete cache[fontFamily];\n}\n\nexport function purgeCache(): void {\n  cache = {};\n}\n"]}