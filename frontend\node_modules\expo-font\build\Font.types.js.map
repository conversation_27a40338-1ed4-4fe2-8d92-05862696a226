{"version": 3, "file": "Font.types.js", "sourceRoot": "", "sources": ["../src/Font.types.ts"], "names": [], "mappings": "AAwBA,cAAc;AACd;;;;;;;;;GASG;AACH,MAAM,CAAN,IAAY,WA8BX;AA9BD,WAAY,WAAW;IACrB;;;;OAIG;IACH,4BAAa,CAAA;IACb;;;OAGG;IACH,4BAAa,CAAA;IACb;;;OAGG;IACH,8BAAe,CAAA;IACf;;;;;;OAMG;IACH,oCAAqB,CAAA;IACrB;;;OAGG;IACH,oCAAqB,CAAA;AACvB,CAAC,EA9BW,WAAW,KAAX,WAAW,QA8BtB", "sourcesContent": ["import { Asset } from 'expo-asset';\n\n// @needsAudit\n/**\n * The different types of assets you can provide to the [`loadAsync()`](#loadasyncfontfamilyorfontmap-source) function.\n * A font source can be a URI, a module ID, or an Expo Asset.\n */\nexport type FontSource = string | number | Asset | FontResource;\n\n// @needsAudit\n/**\n * An object used to dictate the resource that is loaded into the provided font namespace when used\n * with [`loadAsync`](#loadasyncfontfamilyorfontmap-source).\n */\nexport type FontResource = {\n  uri?: string | number;\n  /**\n   * Sets the [`font-display`](#fontdisplay) property for a given typeface in the browser.\n   * @platform web\n   */\n  display?: FontDisplay;\n  default?: string;\n};\n\n// @needsAudit\n/**\n * Sets the [font-display](https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display)\n * for a given typeface. The default font value on web is `FontDisplay.AUTO`.\n * Even though setting the `fontDisplay` does nothing on native platforms, the default behavior\n * emulates `FontDisplay.SWAP` on flagship devices like iOS, Samsung, Pixel, etc. Default\n * functionality varies on One Plus devices. In the browser this value is set in the generated\n * `@font-face` CSS block and not as a style property meaning you cannot dynamically change this\n * value based on the element it's used in.\n * @platform web\n */\nexport enum FontDisplay {\n  /**\n   * __(Default)__ The font display strategy is defined by the user agent or platform.\n   * This generally defaults to the text being invisible until the font is loaded.\n   * Good for buttons or banners that require a specific treatment.\n   */\n  AUTO = 'auto',\n  /**\n   * Fallback text is rendered immediately with a default font while the desired font is loaded.\n   * This is good for making the content appear to load instantly and is usually preferred.\n   */\n  SWAP = 'swap',\n  /**\n   * The text will be invisible until the font has loaded. If the font fails to load then nothing\n   * will appear - it's best to turn this off when debugging missing text.\n   */\n  BLOCK = 'block',\n  /**\n   * Splits the behavior between `SWAP` and `BLOCK`.\n   * There will be a [100ms timeout](https://developers.google.com/web/updates/2016/02/font-display?hl=en)\n   * where the text with a custom font is invisible, after that the text will either swap to the\n   * styled text or it'll show the unstyled text and continue to load the custom font. This is good\n   * for buttons that need a custom font but should also be quickly available to screen-readers.\n   */\n  FALLBACK = 'fallback',\n  /**\n   * This works almost identically to `FALLBACK`, the only difference is that the browser will\n   * decide to load the font based on slow connection speed or critical resource demand.\n   */\n  OPTIONAL = 'optional',\n}\n\n// @needsAudit\n/**\n * Object used to query fonts for unloading.\n * @hidden\n */\nexport type UnloadFontOptions = Pick<FontResource, 'display'>;\n"]}