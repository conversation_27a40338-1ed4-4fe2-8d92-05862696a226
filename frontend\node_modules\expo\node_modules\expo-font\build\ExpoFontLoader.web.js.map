{"version": 3, "file": "ExpoFontLoader.web.js", "sourceRoot": "", "sources": ["../src/ExpoFontLoader.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAClE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAI5C,OAAO,EAAE,WAAW,EAAgB,MAAM,cAAc,CAAC;AAEzD,SAAS,qBAAqB;IAC5B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,UAAU,GAAG,eAAe,EAAE,CAAC;IACrC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,CAAE,UAAU,CAAC,KAAuB,CAAC,CAAC,CAAC,IAAI,CAAC;AACvE,CAAC;AAID,SAAS,gBAAgB;IACvB,MAAM,KAAK,GAAG,qBAAqB,EAAE,CAAC;IACtC,IAAI,KAAK,EAAE,CAAC;QACV,4BAA4B;QAC5B,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;QAElC,MAAM,KAAK,GAAe,EAAE,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,IAAI,YAAY,eAAe,EAAE,CAAC;gBACpC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,gCAAgC,CACvC,cAAsB,EACtB,OAA2B;IAE3B,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC;IACjC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QAC/B,OAAO,CACL,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,cAAc;YACxC,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,KAAM,IAAI,CAAC,KAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAC1F,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,aAAa,GAA2D,IAAI,GAAG,EAAE,CAAC;AAExF,SAAS,eAAe;IAUtB,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACpB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;IAC5D,sDAAsD;IACtD,OAAO;QACL;YACE,MAAM,EAAE,OAAO;YACf,QAAQ,EAAE,GAAG;YACb,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,UAAU;SACjB;QACD,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC5B,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,UAAU;YAChB,EAAE,EAAE,MAAM;YACV,WAAW,EAAE,EAAE;SAChB,CAAC,CAAC;KACJ,CAAC;AACJ,CAAC;AAED,MAAM,cAAc,GAAmC;IACrD,KAAK,CAAC,cAAc;QAClB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAE1C,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAC5C,IAAI,OAAO,IAAI,OAAO,YAAY,gBAAgB,EAAE,CAAC;YACnD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,cAAsB,EAAE,OAA2B;QACnE,MAAM,KAAK,GAAG,qBAAqB,EAAE,CAAC;QACtC,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,MAAM,KAAK,GAAG,gCAAgC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACxE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,kBAAkB;QAChB,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;QAEnC,OAAO,QAAQ;aACZ,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACf,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,OAAO;oBACV,OAAO,cAAc,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,UAAU,CAAC;gBACjE,KAAK,MAAM;oBACT,OAAO,cAAc,OAAO,CAAC,GAAG,WAAW,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,EAAE,kBAAkB,OAAO,CAAC,WAAW,MAAM,CAAC;gBACxH;oBACE,OAAO,EAAE,CAAC;YACd,CAAC;QACH,CAAC,CAAC;aACD,MAAM,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,kBAAkB;QAChB,aAAa,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,cAAc;QACZ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC;QACD,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;IAED,QAAQ,CAAC,cAAsB,EAAE,WAA8B,EAAE;QAC/D,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBAClD,OAAO,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,gCAAgC,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IAChF,CAAC;IAED,uHAAuH;IACvH,wFAAwF;IACxF,SAAS,CAAC,cAAsB,EAAE,QAAsB;QACtD,IAAI,OAAO,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC5C,6EAA6E;YAC7E,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,0DAA0D,OAAO,QAAQ,EAAE,CAC5E,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,aAAa,CAAC,GAAG,CAAC;gBAChB,IAAI,EAAE,cAAc;gBACpB,GAAG,EAAE,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC;gBACrD,kCAAkC;gBAClC,UAAU,EAAE,QAAQ,CAAC,GAAI;aAC1B,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;QACxF,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,UAAU,CAClB,qBAAqB,EACrB,0EAA0E,CAC3E,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,eAAe,EAAE,CAAC;QAChC,QAAQ,CAAC,IAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAElC,MAAM,GAAG,GAAG,gCAAgC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,eAAe,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,8BAA8B,EAAE,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,IAAI,YAAY,CAAC,cAAc,EAAE;YACtC,4FAA4F;YAC5F,OAAO,EAAE,QAAQ,CAAC,OAAO;SAC1B,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACtB,CAAC;CACF,CAAC;AAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,WAAW,CAAC;AAEhF,SAAS,oBAAoB;IAC3B,OAAO,cAAc,CAAC;AACxB,CAAC;AACD,MAAM,QAAQ,GAAG,QAAQ;IACvB,CAAC,CAAC,cAAc;IAChB,CAAC,CAAC,gFAAgF;QAChF,4FAA4F;QAC5F,2DAA2D;QAC3D,iBAAiB,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;AAE9D,eAAe,QAAiC,CAAC;AAEjD,MAAM,EAAE,GAAG,sBAAsB,CAAC;AAElC,SAAS,eAAe;IACtB,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;IAC5C,IAAI,OAAO,IAAI,OAAO,YAAY,gBAAgB,EAAE,CAAC;QACnD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACrD,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC;IAErB,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,UAAkB,EAAE,QAAsB;IAC/E,OAAO,2BAA2B,UAAU,cAAc,QAAQ,CAAC,GAAG,mBACpE,QAAQ,CAAC,OAAO,IAAI,WAAW,CAAC,IAClC,GAAG,CAAC;AACN,CAAC;AAED,SAAS,eAAe,CAAC,UAAkB,EAAE,QAAsB;IACjE,MAAM,SAAS,GAAG,sBAAsB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAE/D,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,+FAA+F;IAC/F,wDAAwD;IACxD,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,YAAmB,CAAC;QAC3C,cAAc,CAAC,UAAU,CAAC,OAAO,GAAG,cAAc,CAAC,UAAU,CAAC,OAAO;YACnE,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS;YAC/C,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;SAAM,CAAC;QACN,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACpD,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,8BAA8B;IACrC,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC;IACvC,2EAA2E;IAC3E,MAAM,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IAChD,MAAM,QAAQ,GAAG,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5E,iGAAiG;IACjG,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1C,oBAAoB;IACpB,MAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC3C,UAAU;IACV,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAChD,OAAO,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;AAC/D,CAAC", "sourcesContent": ["import { CodedError, registerWebModule } from 'expo-modules-core';\nimport FontObserver from 'fontfaceobserver';\n\nimport type { ExpoFontLoaderModule } from './ExpoFontLoader';\nimport { UnloadFontOptions } from './Font';\nimport { FontDisplay, FontResource } from './Font.types';\n\nfunction getFontFaceStyleSheet(): CSSStyleSheet | null {\n  if (typeof window === 'undefined') {\n    return null;\n  }\n  const styleSheet = getStyleElement();\n  return styleSheet.sheet ? (styleSheet.sheet as CSSStyleSheet) : null;\n}\n\ntype RuleItem = { rule: CSSFontFaceRule; index: number };\n\nfunction getFontFaceRules(): RuleItem[] {\n  const sheet = getFontFaceStyleSheet();\n  if (sheet) {\n    // @ts-ignore: rule iterator\n    const rules = [...sheet.cssRules];\n\n    const items: RuleItem[] = [];\n\n    for (let i = 0; i < rules.length; i++) {\n      const rule = rules[i];\n      if (rule instanceof CSSFontFaceRule) {\n        items.push({ rule, index: i });\n      }\n    }\n    return items;\n  }\n  return [];\n}\n\nfunction getFontFaceRulesMatchingResource(\n  fontFamilyName: string,\n  options?: UnloadFontOptions\n): RuleItem[] {\n  const rules = getFontFaceRules();\n  return rules.filter(({ rule }) => {\n    return (\n      rule.style.fontFamily === fontFamilyName &&\n      (options && options.display ? options.display === (rule.style as any).fontDisplay : true)\n    );\n  });\n}\n\nconst serverContext: Set<{ name: string; css: string; resourceId: string }> = new Set();\n\nfunction getHeadElements(): {\n  $$type: string;\n  rel?: string;\n  href?: string;\n  as?: string;\n  crossorigin?: string;\n  children?: string;\n  id?: string;\n  type?: string;\n}[] {\n  const entries = [...serverContext.entries()];\n  if (!entries.length) {\n    return [];\n  }\n  const css = entries.map(([{ css }]) => css).join('\\n');\n  const links = entries.map(([{ resourceId }]) => resourceId);\n  // TODO: Maybe return nothing if no fonts were loaded.\n  return [\n    {\n      $$type: 'style',\n      children: css,\n      id: ID,\n      type: 'text/css',\n    },\n    ...links.map((resourceId) => ({\n      $$type: 'link',\n      rel: 'preload',\n      href: resourceId,\n      as: 'font',\n      crossorigin: '',\n    })),\n  ];\n}\n\nconst ExpoFontLoader: Required<ExpoFontLoaderModule> = {\n  async unloadAllAsync(): Promise<void> {\n    if (typeof window === 'undefined') return;\n\n    const element = document.getElementById(ID);\n    if (element && element instanceof HTMLStyleElement) {\n      document.removeChild(element);\n    }\n  },\n\n  async unloadAsync(fontFamilyName: string, options?: UnloadFontOptions): Promise<void> {\n    const sheet = getFontFaceStyleSheet();\n    if (!sheet) return;\n    const items = getFontFaceRulesMatchingResource(fontFamilyName, options);\n    for (const item of items) {\n      sheet.deleteRule(item.index);\n    }\n  },\n\n  getServerResources(): string[] {\n    const elements = getHeadElements();\n\n    return elements\n      .map((element) => {\n        switch (element.$$type) {\n          case 'style':\n            return `<style id=\"${element.id}\">${element.children}</style>`;\n          case 'link':\n            return `<link rel=\"${element.rel}\" href=\"${element.href}\" as=\"${element.as}\" crossorigin=\"${element.crossorigin}\" />`;\n          default:\n            return '';\n        }\n      })\n      .filter(Boolean);\n  },\n\n  resetServerContext() {\n    serverContext.clear();\n  },\n\n  getLoadedFonts(): string[] {\n    if (typeof window === 'undefined') {\n      return [...serverContext.values()].map(({ name }) => name);\n    }\n    const rules = getFontFaceRules();\n    return rules.map(({ rule }) => rule.style.fontFamily);\n  },\n\n  isLoaded(fontFamilyName: string, resource: UnloadFontOptions = {}): boolean {\n    if (typeof window === 'undefined') {\n      return !![...serverContext.values()].find((asset) => {\n        return asset.name === fontFamilyName;\n      });\n    }\n    return getFontFaceRulesMatchingResource(fontFamilyName, resource)?.length > 0;\n  },\n\n  // NOTE(vonovak): This is used in RN vector-icons to load fonts dynamically on web. Changing the signature is breaking.\n  // NOTE(EvanBacon): No async keyword! This cannot return a promise in Node environments.\n  loadAsync(fontFamilyName: string, resource: FontResource): Promise<void> {\n    if (__DEV__ && typeof resource !== 'object') {\n      // to help devving on web, where loadAsync interface is different from native\n      throw new CodedError(\n        'ERR_FONT_SOURCE',\n        `Expected font resource of type \\`object\\` instead got: ${typeof resource}`\n      );\n    }\n    if (typeof window === 'undefined') {\n      serverContext.add({\n        name: fontFamilyName,\n        css: _createWebFontTemplate(fontFamilyName, resource),\n        // @ts-expect-error: typeof string\n        resourceId: resource.uri!,\n      });\n      return Promise.resolve();\n    }\n\n    const canInjectStyle = document.head && typeof document.head.appendChild === 'function';\n    if (!canInjectStyle) {\n      throw new CodedError(\n        'ERR_WEB_ENVIRONMENT',\n        `The browser's \\`document.head\\` element doesn't support injecting fonts.`\n      );\n    }\n\n    const style = getStyleElement();\n    document.head!.appendChild(style);\n\n    const res = getFontFaceRulesMatchingResource(fontFamilyName, resource);\n    if (!res.length) {\n      _createWebStyle(fontFamilyName, resource);\n    }\n\n    if (!isFontLoadingListenerSupported()) {\n      return Promise.resolve();\n    }\n\n    return new FontObserver(fontFamilyName, {\n      // @ts-expect-error: TODO(@kitten): Typings indicate that the polyfill may not support this?\n      display: resource.display,\n    }).load(null, 6000);\n  },\n};\n\nconst isServer = process.env.EXPO_OS === 'web' && typeof window === 'undefined';\n\nfunction createExpoFontLoader() {\n  return ExpoFontLoader;\n}\nconst toExport = isServer\n  ? ExpoFontLoader\n  : // @ts-expect-error: registerWebModule calls `new` on the module implementation.\n    // Normally that'd be a class but that doesn't work on server, so we use a function instead.\n    // TS doesn't like that but we don't need it to be a class.\n    registerWebModule(createExpoFontLoader, 'ExpoFontLoader');\n\nexport default toExport as typeof ExpoFontLoader;\n\nconst ID = 'expo-generated-fonts';\n\nfunction getStyleElement(): HTMLStyleElement {\n  const element = document.getElementById(ID);\n  if (element && element instanceof HTMLStyleElement) {\n    return element;\n  }\n  const styleElement = document.createElement('style');\n  styleElement.id = ID;\n\n  return styleElement;\n}\n\nexport function _createWebFontTemplate(fontFamily: string, resource: FontResource): string {\n  return `@font-face{font-family:\"${fontFamily}\";src:url(\"${resource.uri}\");font-display:${\n    resource.display || FontDisplay.AUTO\n  }}`;\n}\n\nfunction _createWebStyle(fontFamily: string, resource: FontResource): HTMLStyleElement {\n  const fontStyle = _createWebFontTemplate(fontFamily, resource);\n\n  const styleElement = getStyleElement();\n  // @ts-ignore: TypeScript does not define HTMLStyleElement::styleSheet. This is just for IE and\n  // possibly can be removed if it's unnecessary on IE 11.\n  if (styleElement.styleSheet) {\n    const styleElementIE = styleElement as any;\n    styleElementIE.styleSheet.cssText = styleElementIE.styleSheet.cssText\n      ? styleElementIE.styleSheet.cssText + fontStyle\n      : fontStyle;\n  } else {\n    const textNode = document.createTextNode(fontStyle);\n    styleElement.appendChild(textNode);\n  }\n  return styleElement;\n}\n\nfunction isFontLoadingListenerSupported(): boolean {\n  const { userAgent } = window.navigator;\n  // WebKit is broken https://github.com/bramstein/fontfaceobserver/issues/95\n  const isIOS = !!userAgent.match(/iPad|iPhone/i);\n  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  // Edge is broken https://github.com/bramstein/fontfaceobserver/issues/109#issuecomment-333356795\n  const isEdge = userAgent.includes('Edge');\n  // Internet Explorer\n  const isIE = userAgent.includes('Trident');\n  // Firefox\n  const isFirefox = userAgent.includes('Firefox');\n  return !isSafari && !isIOS && !isEdge && !isIE && !isFirefox;\n}\n"]}