{"version": 3, "file": "FontUtils.js", "sourceRoot": "", "sources": ["../src/FontUtils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAE5C,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAK5C;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,MAAc,EACd,OAA8B;IAE9B,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,kCAAkC,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,MAAM,aAAa,CAAC,kBAAkB,CAAC,MAAM,EAAE;QACpD,GAAG,OAAO;QACV,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;KAChE,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\nimport { processColor } from 'react-native';\n\nimport ExpoFontUtils from './ExpoFontUtils';\nimport type { RenderToImageOptions, RenderToImageResult } from './FontUtils.types';\n\nexport type { RenderToImageOptions, RenderToImageResult };\n\n/**\n * Creates an image with provided text.\n * @param glyphs Text to be exported.\n * @param options RenderToImageOptions.\n * @return Promise which fulfils with uri to image.\n * @platform android\n * @platform ios\n */\nexport async function renderToImageAsync(\n  glyphs: string,\n  options?: RenderToImageOptions\n): Promise<RenderToImageResult> {\n  if (!ExpoFontUtils) {\n    throw new UnavailabilityError('expo-font', 'ExpoFontUtils.renderToImageAsync');\n  }\n\n  return await ExpoFontUtils.renderToImageAsync(glyphs, {\n    ...options,\n    color: options?.color ? processColor(options.color) : undefined,\n  });\n}\n"]}