This design is modern, clean, and uses a card-based layout with clear visual hierarchy to present information effectively.

---

### **\#\# Color Scheme 🎨**

The palette is professional and energetic, using a strong teal as the primary color, accented with a warm orange for alerts and important statuses.

* **Primary Action (Teal):** Used for main cards, primary buttons, and active states.  
  * \#0F766E (Dark Teal)  
* **Primary Background (Light Teal):** Used for secondary buttons or highlighted areas.  
  * \#CCFBF1 (Light Teal/Mint)  
* **Accent / Warning (Orange):** For notifications, alerts like "Missed Scheduled," and "In progress" tags.  
  * \#F97316 (Vibrant Orange)  
* **Text \- Primary:** For headings and important text.  
  * \#1F2937 (Almost Black)  
* **Text \- Secondary:** For subheadings, location details, and less important information.  
  * \#6B7281 (Medium Gray)  
* **Text \- On Dark BG:** For text on primary color backgrounds.  
  * \#FFFFFF (White)  
* **Background \- Screen:** The main background color for the screen.  
  * \#F8FAFC (Very Light Gray)  
* **Background \- Card:** The background for all white cards.  
  * \#FFFFFF (White)  
* **Borders & Outlines:** For outlined buttons and dividers.  
  * \#E5E7EB (Light Gray)

---

### **\#\# Typography ✒️**

A clean, sans-serif font family is used. **Roboto** from Google Fonts would be excellent choices and are easy to integrate with Expo.

* **Font Family:** Roboto  
* **Heading 1 (Welcome):**  
  * **Font Size:** 24px  
  * **Weight:** 600 (Semi-Bold)  
  * **Usage:** "Welcome Louis\!"  
* **Large Display (Timer/Stats):**  
  * **Font Size:** 32px  
  * **Weight:** 500 (Medium)  
  * **Usage:** The countdown timer 01 : 35 : 40 and the large stat numbers 7, 12, 5\.  
* **Card Title (Name):**  
  * **Font Size:** 16px  
  * **Weight:** 600 (Semi-Bold)  
  * **Usage:** "Melisa Adam"  
* **Body:**  
  * **Font Size:** 14px  
  * **Weight:** 400 (Regular)  
  * **Usage:** Location details, service names, descriptive text.  
* **Small/Label Text:**  
  * **Font Size:** 12px  
  * **Weight:** 400 (Regular)  
  * **Usage:** Labels under the stat numbers ("Missed Scheduled"), date/time in cards.  
* **Button Text:**  
  * **Font Size:** 14px  
  * **Weight:** 600 (Semi-Bold)  
  * **Usage:** "Clock-Out", "Clock-In Now"

---

### **\#\# Layout & Spacing**

The design uses a consistent spacing system (based on an 8px grid) and rounded elements to create a soft, modern feel.

* **Grid & Padding:**  
  * **Screen Padding:** 20px horizontal.  
  * **Card Padding:** 16px for all sides.  
  * **Gap Between Elements:** 12px or 16px between cards and sections.  
* **Border Radius:**  
  * **Large Cards & Buttons:** 12px.  
  * **Small elements (Tags):** 20px (pill-shaped).  
* **Shadows:**  
  * Subtle shadows on cards to create depth. Use a soft black with low opacity.  
  * **React Native Style:**  
    JavaScript  
    shadowColor: "\#000",  
    shadowOffset: { width: 0, height: 2 },  
    shadowOpacity: 0.1,  
    shadowRadius: 4,  
    elevation: 3, // for Android

---

### **\#\# Component Breakdown (For React)**

Here are the key reusable components you'll need to build.

* **\<StatusCard\> (The top teal card)**  
  * **Props:** user (object with name, avatarUrl), location, timeRange, onClockOut (function).  
  * **Notes:** Has a fixed dark teal background (\#0F766E) with white text. Includes a prominent "Clock-Out" button.  
* **\<SummaryCard\> (For Missed, Upcoming, Completed)**  
  * **Props:** value (number), label (string), valueColor (string, e.g., the accent orange).  
  * **Notes:** A simple flex-container with centered content. The two smaller cards are wrapped in a container with flexDirection: 'row' and a gap.  
* **\<ScheduleCard\> (The main list item)**  
  * **Props:** status ('scheduled' | 'in-progress'), user, serviceName, location, dateTime, onClockIn, onClockOut, onViewProgress.  
  * **Logic:**  
    * Conditionally renders a status tag ("Scheduled", "In progress") with different background colors.  
    * Conditionally renders the buttons at the bottom based on the status prop.  
    * The date/time container has a light background (\#CCFBF1 or \#E5E7EB).  
* **\<Button\>**  
  * **Props:** title, onPress, variant ('solid' | 'outline'), color.  
  * **Notes:**  
    * **Solid:** Background is the primary action color (\#0F766E), text is white.  
    * **Outline:** Transparent background, colored border and text.  
* **\<IconLabel\>**  
  * **Props:** iconName, text.  
  * **Notes:** A simple component to display an icon next to a piece of text (e.g., location pin and address).

---

### **\#\# Icons 🧭**

For icons, use a library like **Expo Vector Icons**. Feather or MaterialCommunityIcons would match this style well.

* **Location:** map-pin  
* **Time:** clock  
* **More Options:** more-horizontal (or dots-horizontal)  
* **Clock-in/out:** log-in / log-out

Good luck with your project\! This design provides a great foundation for a clean and user-friendly application. 👍