{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../src/server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAEpE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AAEtE;;;GAGG;AACH,MAAM,UAAU,kBAAkB;IAChC,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACvC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;IACnE,CAAC;IACD,OAAO,cAAc,CAAC,kBAAkB,EAAE,CAAC;AAC7C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB;IAChC,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACvC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;IACnE,CAAC;IACD,OAAO,cAAc,CAAC,kBAAkB,EAAE,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,UAAkB,EAAE,MAA0B;IAC/E,2BAA2B;IAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,iDAAiD,UAAU,MAAM,MAAM,sEAAsE,UAAU,GAAG,CAC3J,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAExC,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACzC,CAAC", "sourcesContent": ["import { CodedError, UnavailabilityError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontSource } from './Font.types';\nimport { getAssetForSource, loadSingleFontAsync } from './FontLoader';\n\n/**\n * @returns the server resources that should be statically extracted.\n * @private\n */\nexport function getServerResources(): string[] {\n  if (!ExpoFontLoader.getServerResources) {\n    throw new UnavailabilityError('expo-font', 'getServerResources');\n  }\n  return ExpoFontLoader.getServerResources();\n}\n\n/**\n * @returns clear the server resources from the global scope.\n * @private\n */\nexport function resetServerContext() {\n  if (!ExpoFontLoader.resetServerContext) {\n    throw new UnavailabilityError('expo-font', 'resetServerContext');\n  }\n  return ExpoFontLoader.resetServerContext();\n}\n\nexport function registerStaticFont(fontFamily: string, source?: FontSource | null) {\n  // MUST BE A SYNC FUNCTION!\n  if (!source) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`\n    );\n  }\n  const asset = getAssetForSource(source);\n\n  loadSingleFontAsync(fontFamily, asset);\n}\n"]}