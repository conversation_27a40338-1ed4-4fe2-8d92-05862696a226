{"version": 3, "file": "ExpoFontLoader.js", "sourceRoot": "", "sources": ["../src/ExpoFontLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAexD,MAAM,CAAC,GACL,OAAO,MAAM,KAAK,WAAW;IAC3B,CAAC,CAAC,oBAAoB;QACpB;YACE,cAAc;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,SAAS;gBACP,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;SACF;IACH,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAC5C,eAAe,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { UnloadFontOptions } from './Font.types';\n\nexport type ExpoFontLoaderModule = {\n  getLoadedFonts: () => string[];\n  loadAsync: (fontFamilyName: string, localUriOrWebAsset: any) => Promise<void>;\n  // the following methods are only available on web\n  unloadAllAsync?: () => Promise<void>;\n  unloadAsync?: (fontFamilyName: string, options?: UnloadFontOptions) => Promise<void>;\n  isLoaded?: (fontFamilyName: string, options?: UnloadFontOptions) => boolean;\n  getServerResources?: () => string[];\n  resetServerContext?: () => void;\n};\n\nconst m: ExpoFontLoaderModule =\n  typeof window === 'undefined'\n    ? // React server mock\n      {\n        getLoadedFonts() {\n          return [];\n        },\n        loadAsync() {\n          return Promise.resolve();\n        },\n      }\n    : requireNativeModule('ExpoFontLoader');\nexport default m;\n"]}